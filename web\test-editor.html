<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑器智能提示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .editor-container {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .debug-output {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Monaco Editor 智能提示测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <p>这个页面用于测试 Monaco Editor 的智能提示功能，特别是 ForEach 循环中的变量提示。</p>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>点击"初始化编辑器"按钮</li>
                <li>在编辑器中输入 <code>ePwGinLinkPW.</code> 并观察智能提示</li>
                <li>检查是否显示 <code>item</code> 字段</li>
                <li>继续输入 <code>ePwGinLinkPW.item.</code> 并观察子字段提示</li>
            </ol>
        </div>

        <button onclick="initializeEditor()">初始化编辑器</button>
        <button onclick="testTypeDefinitions()">测试类型定义</button>
        <button onclick="clearDebugOutput()">清空输出</button>

        <div id="editor" class="editor-container"></div>
        
        <div id="debugOutput" class="debug-output">等待初始化...</div>
    </div>

    <!-- Monaco Editor -->
    <script src="https://unpkg.com/monaco-editor@0.34.1/min/vs/loader.js"></script>
    <script>
        let editor = null;
        let monaco = null;

        function appendToDebugOutput(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearDebugOutput() {
            document.getElementById('debugOutput').textContent = '';
        }

        // 模拟变量数据
        const mockVariables = [
            {
                id: 'ePwGinLinkPW',
                key: 'ePwGinLinkPW',
                path: 'ePwGinLinkPW',
                description: 'ForEach循环变量',
                type: 'object',
                children: [
                    {
                        id: 'item',
                        key: 'item',
                        path: 'ePwGinLinkPW.item',
                        description: '数据项',
                        type: 'object',
                        children: [
                            {
                                id: 'erp_org_code',
                                key: 'erp_org_code',
                                path: 'ePwGinLinkPW.item.erp_org_code',
                                description: 'ERP组织代码',
                                type: 'string'
                            },
                            {
                                id: 'equipment_name',
                                key: 'equipment_name',
                                path: 'ePwGinLinkPW.item.equipment_name',
                                description: '设备名称',
                                type: 'string'
                            }
                        ]
                    }
                ]
            }
        ];

        function generateTypeDefinitions(variables) {
            let typeDefinitions = '// 自动生成的类型定义\n\n';
            
            function generateInterface(vars, interfaceName) {
                let interfaceCode = `interface ${interfaceName} {\n`;
                
                vars.forEach(variable => {
                    if (variable.children && variable.children.length > 0) {
                        const childInterfaceName = `${interfaceName}_${variable.key}`;
                        typeDefinitions += generateInterface(variable.children, childInterfaceName) + '\n\n';
                        interfaceCode += `  /** ${variable.description || variable.key} */\n`;
                        interfaceCode += `  ${variable.key}: ${childInterfaceName};\n`;
                    } else {
                        const tsType = variable.type === 'string' ? 'string' : 
                                     variable.type === 'number' ? 'number' : 
                                     variable.type === 'boolean' ? 'boolean' : 'any';
                        interfaceCode += `  /** ${variable.description || variable.key} */\n`;
                        interfaceCode += `  ${variable.key}: ${tsType};\n`;
                    }
                });
                
                interfaceCode += '}';
                return interfaceCode;
            }

            // 生成当前变量接口
            typeDefinitions += generateInterface(variables, 'CurrentVariables') + '\n\n';
            
            // 生成变量声明
            variables.forEach(variable => {
                const tsType = variable.children && variable.children.length > 0 ? 
                              `CurrentVariables_${variable.key}` : 
                              (variable.type === 'string' ? 'string' : 
                               variable.type === 'number' ? 'number' : 
                               variable.type === 'boolean' ? 'boolean' : 'any');
                typeDefinitions += `/** ${variable.description || variable.key} */\n`;
                typeDefinitions += `declare const ${variable.key}: ${tsType};\n\n`;
            });

            return typeDefinitions;
        }

        function initializeEditor() {
            appendToDebugOutput('开始初始化 Monaco Editor...');
            
            require.config({ paths: { vs: 'https://unpkg.com/monaco-editor@0.34.1/min/vs' } });
            
            require(['vs/editor/editor.main'], function() {
                monaco = window.monaco;
                appendToDebugOutput('Monaco Editor 加载完成');
                
                // 生成类型定义
                const typeDefinitions = generateTypeDefinitions(mockVariables);
                appendToDebugOutput('生成的类型定义:\n' + typeDefinitions);
                
                // 添加类型定义到 Monaco
                monaco.languages.typescript.typescriptDefaults.addExtraLib(
                    typeDefinitions, 
                    'ts:custom-variables.d.ts'
                );
                
                // 创建编辑器
                editor = monaco.editor.create(document.getElementById('editor'), {
                    value: [
                        '// 测试智能提示',
                        '// 输入 ePwGinLinkPW. 应该显示 item 字段',
                        '// 输入 ePwGinLinkPW.item. 应该显示 erp_org_code 和 equipment_name',
                        '',
                        'var lastTime = Utils.DATE_TO_STRING(_data.JOB_LAST_TIME, "yyyy-MM-dd HH:mm:ss");',
                        '',
                        '// 在下面测试智能提示:',
                        'ePwGinLinkPW.',
                        ''
                    ].join('\n'),
                    language: 'typescript',
                    theme: 'vs-dark',
                    automaticLayout: true,
                    suggestOnTriggerCharacters: true,
                    quickSuggestions: true,
                    wordBasedSuggestions: false
                });
                
                appendToDebugOutput('编辑器创建完成，可以开始测试智能提示');
                appendToDebugOutput('提示：按 Ctrl+Space 可以手动触发智能提示');
            });
        }

        function testTypeDefinitions() {
            if (!monaco) {
                appendToDebugOutput('Monaco Editor 尚未初始化');
                return;
            }
            
            appendToDebugOutput('测试类型定义...');
            
            // 获取当前的类型定义
            const libs = monaco.languages.typescript.typescriptDefaults.getExtraLibs();
            appendToDebugOutput('当前类型库数量: ' + Object.keys(libs).length);
            
            Object.keys(libs).forEach(uri => {
                if (uri.includes('custom-variables')) {
                    appendToDebugOutput('找到自定义变量类型定义: ' + uri);
                    appendToDebugOutput('内容预览:\n' + libs[uri].content.substring(0, 500) + '...');
                }
            });
        }

        // 页面加载完成后的初始化
        appendToDebugOutput('页面加载完成，点击"初始化编辑器"开始测试');
    </script>
</body>
</html>
