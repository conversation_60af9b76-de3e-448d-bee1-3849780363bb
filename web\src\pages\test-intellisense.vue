<template>
  <div class="test-container">
    <h1>智能提示测试页面</h1>
    
    <div class="test-section">
      <h3>问题描述</h3>
      <p>在 ForEach 循环中，<code>ePwGinLinkPW.item</code> 字段缺失智能提示。</p>
    </div>

    <div class="test-section">
      <h3>测试编辑器</h3>
      <p>在下面的编辑器中输入 <code>ePwGinLinkPW.</code> 应该显示 <code>item</code> 字段的智能提示。</p>
      
      <editor
        ref="editorRef"
        v-model:value="scriptValue"
        language="typescript"
        :enable-intellisense="true"
        :current-variables="mockCurrentVariables"
        :local-variables="[]"
        :global-variables="[]"
        :functions="[]"
        style="height: 300px; border: 1px solid #ddd; border-radius: 4px;"
      />
    </div>

    <div class="test-section">
      <h3>调试信息</h3>
      <button @click="testIntelliSense">测试智能提示数据</button>
      <button @click="clearDebugOutput">清空输出</button>
      
      <div class="debug-output" ref="debugOutput">
        {{ debugOutput }}
      </div>
    </div>

    <div class="test-section">
      <h3>变量数据结构</h3>
      <pre class="json-output">{{ JSON.stringify(mockCurrentVariables, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Editor from '@/components/editor/index.vue';
import { updateIntelliSenseData } from '@/components/editor/scriptCompletion';

// 模拟 ForEach 循环中的变量数据
const mockCurrentVariables = ref([
  {
    id: 'ePwGinLinkPW',
    key: 'ePwGinLinkPW',
    path: 'ePwGinLinkPW',
    pathDescription: 'ForEach循环变量路径',
    description: 'ForEach循环变量',
    type: 'object',
    children: [
      {
        id: 'item',
        key: 'item',
        path: 'ePwGinLinkPW.item',
        pathDescription: '数据项路径',
        description: '数据项',
        type: 'object',
        children: [
          {
            id: 'erp_org_code',
            key: 'erp_org_code',
            path: 'ePwGinLinkPW.item.erp_org_code',
            pathDescription: 'ERP组织代码路径',
            description: 'ERP组织代码',
            type: 'string'
          },
          {
            id: 'equipment_name',
            key: 'equipment_name',
            path: 'ePwGinLinkPW.item.equipment_name',
            pathDescription: '设备名称路径',
            description: '设备名称',
            type: 'string'
          },
          {
            id: 'status',
            key: 'status',
            path: 'ePwGinLinkPW.item.status',
            pathDescription: '状态路径',
            description: '设备状态',
            type: 'number'
          }
        ]
      }
    ]
  }
]);

const scriptValue = ref(`// 测试智能提示
// 在下面输入 ePwGinLinkPW. 应该显示 item 字段
// 继续输入 ePwGinLinkPW.item. 应该显示子字段

var lastTime = Utils.DATE_TO_STRING(_data.JOB_LAST_TIME, "yyyy-MM-dd HH:mm:ss");

// 测试区域：
ePwGinLinkPW.
`);

const debugOutput = ref('页面加载完成，等待测试...\n');
const debugOutputRef = ref<HTMLElement>();

const appendToDebugOutput = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  debugOutput.value += `[${timestamp}] ${message}\n`;
  
  // 滚动到底部
  setTimeout(() => {
    if (debugOutputRef.value) {
      debugOutputRef.value.scrollTop = debugOutputRef.value.scrollHeight;
    }
  }, 10);
};

const clearDebugOutput = () => {
  debugOutput.value = '';
};

const testIntelliSense = () => {
  appendToDebugOutput('开始测试智能提示...');
  appendToDebugOutput('当前变量数据: ' + JSON.stringify(mockCurrentVariables.value, null, 2));
  
  try {
    // 手动调用智能提示更新
    updateIntelliSenseData({
      currentVariables: mockCurrentVariables.value,
      localVariables: [],
      globalVariables: [],
      functions: []
    });
    
    appendToDebugOutput('智能提示数据更新完成');
    appendToDebugOutput('请在编辑器中输入 ePwGinLinkPW. 测试智能提示');
  } catch (error) {
    appendToDebugOutput('测试失败: ' + error.message);
  }
};

// 监听控制台输出
const originalConsoleLog = console.log;
console.log = function(...args) {
  originalConsoleLog.apply(console, args);
  
  // 捕获智能提示相关的日志
  if (args.length > 0 && typeof args[0] === 'string') {
    const message = args[0];
    if (message.includes('更新当前变量') || 
        message.includes('生成接口') || 
        message.includes('处理变量') ||
        message.includes('类型定义') ||
        message.includes('ePwGinLinkPW')) {
      appendToDebugOutput('Console: ' + args.join(' '));
    }
  }
};

onMounted(() => {
  appendToDebugOutput('组件挂载完成');
  
  // 自动测试一次
  setTimeout(() => {
    testIntelliSense();
  }, 1000);
});
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

.debug-output {
  background: #2d2d2d;
  color: #f8f8f2;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  margin: 10px 0;
}

.json-output {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  overflow-x: auto;
  border: 1px solid #ddd;
}

button {
  background: #007acc;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px;
}

button:hover {
  background: #005a9e;
}

code {
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}
</style>
