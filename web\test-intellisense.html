<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能提示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .code-block {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            border-left: 4px solid #007acc;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .debug-output {
            background: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>智能提示系统测试</h1>
        
        <div class="test-section">
            <h3>问题描述</h3>
            <p>在 ForEach 循环中，<code>ePwGinLinkPW.item</code> 字段缺失智能提示。</p>
            <div class="code-block">
                var lastTime = Utils.DATE_TO_STRING(_data.JOB_LAST_TIME, "yyyy-MM-dd HH:mm:ss");<br>
                (ePwGinLinkPW.item.erp_org_code) // 这里应该有 item 字段的智能提示
            </div>
        </div>

        <div class="test-section">
            <h3>测试步骤</h3>
            <ol>
                <li>打开开发者工具的控制台</li>
                <li>点击下面的测试按钮</li>
                <li>查看控制台输出的调试信息</li>
                <li>检查是否正确生成了包含 item 字段的类型定义</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>模拟测试数据</h3>
            <button onclick="testIntelliSense()">测试智能提示数据</button>
            <button onclick="clearDebugOutput()">清空输出</button>
            <div id="status" class="status info">点击测试按钮开始测试</div>
            <div id="debugOutput" class="debug-output"></div>
        </div>

        <div class="test-section">
            <h3>预期结果</h3>
            <ul>
                <li>控制台应该显示变量结构的详细信息</li>
                <li>应该包含 ePwGinLinkPW 变量及其 item 子字段</li>
                <li>类型定义应该正确生成嵌套接口</li>
                <li>Monaco Editor 应该能够识别 ePwGinLinkPW.item 的字段</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟智能提示数据结构
        const mockCurrentVariables = [
            {
                id: 'ePwGinLinkPW',
                key: 'ePwGinLinkPW',
                path: 'ePwGinLinkPW',
                description: 'ForEach循环变量',
                type: 'object',
                children: [
                    {
                        id: 'item',
                        key: 'item',
                        path: 'ePwGinLinkPW.item',
                        description: '数据项',
                        type: 'object',
                        children: [
                            {
                                id: 'erp_org_code',
                                key: 'erp_org_code',
                                path: 'ePwGinLinkPW.item.erp_org_code',
                                description: 'ERP组织代码',
                                type: 'string'
                            },
                            {
                                id: 'equipment_name',
                                key: 'equipment_name',
                                path: 'ePwGinLinkPW.item.equipment_name',
                                description: '设备名称',
                                type: 'string'
                            }
                        ]
                    }
                ]
            }
        ];

        function appendToDebugOutput(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearDebugOutput() {
            document.getElementById('debugOutput').textContent = '';
            document.getElementById('status').textContent = '输出已清空';
            document.getElementById('status').className = 'status info';
        }

        function testIntelliSense() {
            const status = document.getElementById('status');
            
            try {
                status.textContent = '正在测试智能提示...';
                status.className = 'status info';
                
                appendToDebugOutput('开始测试智能提示系统');
                appendToDebugOutput('模拟变量数据: ' + JSON.stringify(mockCurrentVariables, null, 2));
                
                // 检查是否有智能提示相关的函数
                if (typeof window.updateIntelliSenseData === 'function') {
                    appendToDebugOutput('找到 updateIntelliSenseData 函数，开始调用...');
                    window.updateIntelliSenseData({
                        currentVariables: mockCurrentVariables
                    });
                    status.textContent = '测试完成，请查看控制台输出';
                    status.className = 'status success';
                } else {
                    appendToDebugOutput('未找到 updateIntelliSenseData 函数');
                    appendToDebugOutput('这可能是因为智能提示模块尚未加载');
                    status.textContent = '智能提示模块未加载，请在实际的编辑器页面中测试';
                    status.className = 'status error';
                }
                
                // 检查 Monaco Editor 是否可用
                if (typeof monaco !== 'undefined') {
                    appendToDebugOutput('Monaco Editor 已加载');
                } else {
                    appendToDebugOutput('Monaco Editor 未加载');
                }
                
            } catch (error) {
                appendToDebugOutput('测试过程中发生错误: ' + error.message);
                status.textContent = '测试失败: ' + error.message;
                status.className = 'status error';
            }
        }

        // 监听控制台输出
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args.length > 0 && typeof args[0] === 'string') {
                if (args[0].includes('更新当前变量') || 
                    args[0].includes('生成接口') || 
                    args[0].includes('处理变量') ||
                    args[0].includes('类型定义')) {
                    appendToDebugOutput('Console: ' + args.join(' '));
                }
            }
        };

        appendToDebugOutput('测试页面已加载，准备就绪');
    </script>
</body>
</html>
