import * as monaco from 'monaco-editor';

// 变量和函数数据接口
interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
  category?: string;
  categoryDisplayName?: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    defaultValue?: any;
    description?: string;
    isVariadic?: boolean;
  }>;
  returnType?: string;
  outputType?: string;
  examples?: Array<{
    title: string;
    code: string;
    result?: string;
    description: string;
  }>;
}

// 全局变量存储
let currentVariables: VariableData[] = [];
let localVariables: VariableData[] = [];
let globalVariables: VariableData[] = [];
let functions: FunctionData[] = [];

// 使用模板字符串生成类型定义 - 支持完整的输入输出参数、描述等
const generateTypeDefinitionsWithTemplate = (variables: VariableData[], interfaceName: string): string => {
  console.log(`生成接口 ${interfaceName}，变量数量:`, variables.length);
  console.log(
    `${interfaceName} 变量详情:`,
    variables.map((v) => ({ key: v.key, type: v.type, hasChildren: !!v.children?.length })),
  );

  /**
   * 递归生成属性定义
   * @param vars 变量数组
   * @param indent 缩进字符串
   * @param parentPath 父级路径，用于生成嵌套接口名
   */
  const generateProperties = (vars: VariableData[], indent: string = '  ', parentPath: string = ''): string => {
    const properties: string[] = [];
    const nestedInterfaces: string[] = [];

    vars.forEach((variable) => {
      const { key, type, description, pathDescription, children } = variable;

      console.log(`处理变量 ${key}:`, { type, hasChildren: !!children?.length, childrenCount: children?.length || 0 });

      // 生成注释 - 支持多行描述
      const comments: string[] = [];
      if (description) comments.push(description);
      if (pathDescription && pathDescription !== description) comments.push(`路径: ${pathDescription}`);
      if (type) comments.push(`类型: ${type}`);

      const commentBlock =
        comments.length > 0
          ? `${indent}/**\n${comments.map((c) => `${indent} * ${c}`).join('\n')}\n${indent} */\n`
          : '';

      // 处理嵌套对象
      if (children && children.length > 0) {
        const nestedInterfaceName = `${interfaceName}_${key}`;
        const nestedPath = parentPath ? `${parentPath}_${key}` : key;

        console.log(`生成嵌套接口 ${nestedInterfaceName}，子变量数量:`, children.length);

        // 生成嵌套接口
        const nestedInterface = generateNestedInterface(children, nestedInterfaceName, nestedPath);
        nestedInterfaces.push(nestedInterface);

        // 添加属性引用
        properties.push(`${commentBlock}${indent}${key}: ${nestedInterfaceName};`);
      } else {
        // 普通属性
        const tsType = getTypeScriptType(type);
        properties.push(`${commentBlock}${indent}${key}: ${tsType};`);
      }
    });

    // 返回嵌套接口定义 + 属性定义
    const allInterfaces = nestedInterfaces.join('\n\n');
    const allProperties = properties.join('\n');

    return allInterfaces ? `${allInterfaces}\n\n${allProperties}` : allProperties;
  };

  /**
   * 生成嵌套接口
   */
  const generateNestedInterface = (vars: VariableData[], interfaceName: string, path: string): string => {
    const properties = generateProperties(vars, '  ', path);

    return `interface ${interfaceName} {
${properties}
}`;
  };

  // 生成主接口
  const properties = generateProperties(variables);

  return `interface ${interfaceName} {
${properties}
}`;
};

// 已移除旧的复杂类型定义生成方法，现在使用简化的模板字符串方法

// 使用模板字符串生成Utils类型定义 - 支持完整的参数描述
const generateUtilsTypeDefinition = (): string => {
  if (functions.length === 0) {
    return 'declare const Utils: any;';
  }

  const utilsMethods = functions
    .map((func) => {
      // 生成参数定义 - 使用模板字符串
      const generateParameters = (): string => {
        if (!func.parameters || func.parameters.length === 0) return '';

        return func.parameters
          .map((param) => {
            const paramType = getTypeScriptType(param.type);
            const optional = !param.required ? '?' : '';

            // 处理可变参数
            if (param.isVariadic) {
              return `...${param.name}: any[]`;
            } else {
              return `${param.name}${optional}: ${paramType}`;
            }
          })
          .join(', ');
      };

      // 生成返回类型
      const returnType = func.returnType ? getTypeScriptType(func.returnType) : 'any';

      // 生成完整的JSDoc注释 - 使用模板字符串
      const generateJSDoc = (): string => {
        const lines = ['  /**'];

        // 函数描述
        if (func.remark) {
          lines.push(`   * ${func.remark}`);
          if (func.label !== func.remark) {
            lines.push(`   * 中文名: ${func.label}`);
          }
        }

        // 参数说明
        if (func.parameters && func.parameters.length > 0) {
          lines.push('   *');
          func.parameters.forEach((param) => {
            const required = param.required ? '必需' : '可选';
            const defaultVal = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
            const desc = param.description || '';
            lines.push(`   * @param ${param.name} {${param.type}} ${required} - ${desc}${defaultVal}`);
          });
        }

        // 返回值说明
        if (func.returnType) {
          lines.push('   *');
          const outputDesc = func.outputType || func.returnType;
          lines.push(`   * @returns {${func.returnType}} ${outputDesc}`);
        }

        // 使用示例
        if (func.examples && func.examples.length > 0) {
          lines.push('   *');
          lines.push('   * @example');
          func.examples.slice(0, 2).forEach((example) => {
            lines.push(`   * // ${example.title}`);
            lines.push(`   * ${example.code}`);
            if (example.result) {
              lines.push(`   * // => ${example.result}`);
            }
            if (example.description) {
              lines.push(`   * // ${example.description}`);
            }
          });
        }

        lines.push('   */');
        return lines.join('\n');
      };

      const parameters = generateParameters();
      const jsdoc = generateJSDoc();

      return `${jsdoc}
  ${func.value}(${parameters}): ${returnType};`;
    })
    .join('\n\n');

  return `declare const Utils: {
${utilsMethods}
};`;
};

// 旧的JSDoc生成函数已被集成到Utils类型定义中

// 生成完整的 TypeScript 类型定义文件
const generateTypeDefinitionFile = (): string => {
  console.log('=== 开始生成类型定义文件 ===');
  console.log('当前变量数量:', currentVariables.length);
  console.log('局部变量数量:', localVariables.length);
  console.log('全局变量数量:', globalVariables.length);

  const sections: string[] = [];

  // 1. 生成嵌套接口定义
  const interfaces: string[] = [];

  if (localVariables.length > 0) {
    console.log('生成局部变量接口...');
    interfaces.push(generateTypeDefinitionsWithTemplate(localVariables, 'LocalVariables'));
  }

  if (globalVariables.length > 0) {
    console.log('生成全局变量接口...');
    interfaces.push(generateTypeDefinitionsWithTemplate(globalVariables, 'GlobalVariables'));
  }

  if (currentVariables.length > 0) {
    console.log('生成当前变量接口...');
    interfaces.push(generateTypeDefinitionsWithTemplate(currentVariables, 'CurrentVariables'));
  }

  if (interfaces.length > 0) {
    sections.push('// ===== 变量接口定义 =====');
    sections.push(...interfaces);
    console.log('接口定义生成完成，数量:', interfaces.length);
  } else {
    console.log('⚠ 没有生成任何接口定义');
  }

  // 2. 生成 _data 对象类型定义
  if (localVariables.length > 0 || globalVariables.length > 0) {
    sections.push('// ===== 数据对象定义 =====');

    const dataProperties: string[] = [];

    // 处理局部变量
    localVariables.forEach((variable) => {
      const { key, type, description, pathDescription, children } = variable;
      const comments = generateComments([description, pathDescription, `类型: ${type}`]);
      const tsType = children && children.length > 0 ? `LocalVariables_${key}` : getTypeScriptType(type);
      dataProperties.push(`${comments}  ${key}: ${tsType};`);
    });

    // 处理全局变量
    globalVariables.forEach((variable) => {
      const { key, type, description, pathDescription, children } = variable;
      const comments = generateComments([description, pathDescription, `类型: ${type}`]);
      const tsType = children && children.length > 0 ? `GlobalVariables_${key}` : getTypeScriptType(type);
      dataProperties.push(`${comments}  ${key}: ${tsType};`);
    });

    if (dataProperties.length > 0) {
      sections.push(`interface DataObject {\n${dataProperties.join('\n')}\n}`);
      sections.push('declare const _data: DataObject;');
    }
  }

  // 3. 生成 Utils 工具类定义
  sections.push('// ===== 工具函数定义 =====');
  sections.push(generateUtilsTypeDefinition());

  // 4. 生成当前变量声明
  if (currentVariables.length > 0) {
    sections.push('// ===== 当前变量声明 =====');

    const declarations = currentVariables.map((variable) => {
      const { key, type, description, pathDescription, children } = variable;
      const comments = generateComments([description, pathDescription, `类型: ${type}`]);
      const tsType = children && children.length > 0 ? `CurrentVariables_${key}` : getTypeScriptType(type);
      return `${comments}declare const ${key}: ${tsType};`;
    });

    sections.push(...declarations);
  }

  return sections.filter((section) => section.trim()).join('\n\n');
};

// 生成注释块的辅助函数
const generateComments = (comments: (string | undefined)[]): string => {
  const validComments = comments.filter((c) => c && c.trim());
  if (validComments.length === 0) return '';

  return `  /**\n${validComments.map((c) => `   * ${c}`).join('\n')}\n   */\n`;
};

// 更新Monaco Editor的类型定义 - 使用原生 addExtraLib 方法
const updateMonacoTypeDefinitions = () => {
  try {
    console.log('开始生成类型定义...');
    console.log('当前变量数量:', currentVariables.length);
    console.log('局部变量数量:', localVariables.length);
    console.log('全局变量数量:', globalVariables.length);

    // 生成完整的类型定义文件
    const typeDefinitions = generateTypeDefinitionFile();

    if (!typeDefinitions.trim()) {
      console.log('没有类型定义需要更新');
      return;
    }

    // 清除之前的类型定义
    try {
      monaco.languages.typescript.typescriptDefaults.setExtraLibs([]);
      monaco.languages.typescript.javascriptDefaults.setExtraLibs([]);
    } catch (error) {
      console.warn('清除旧类型定义失败:', error);
    }

    // 使用原生 addExtraLib 方法添加类型定义
    const libUri = 'ts:custom-variables.d.ts';
    monaco.languages.typescript.typescriptDefaults.addExtraLib(typeDefinitions, libUri);
    monaco.languages.typescript.javascriptDefaults.addExtraLib(typeDefinitions, libUri);

    console.log('已更新Monaco Editor类型定义');
    console.log('类型定义内容:', typeDefinitions);

    // 检查是否包含特定变量的定义
    if (typeDefinitions.includes('ePwGinLinkPW')) {
      console.log('✓ 类型定义中包含 ePwGinLinkPW');
      // 提取相关部分进行详细检查
      const lines = typeDefinitions.split('\n');
      const relevantLines = lines.filter(
        (line) =>
          line.includes('ePwGinLinkPW') ||
          line.includes('item') ||
          (line.trim().startsWith('interface') &&
            lines.indexOf(line) > 0 &&
            lines
              .slice(Math.max(0, lines.indexOf(line) - 5), lines.indexOf(line) + 20)
              .some((l) => l.includes('ePwGinLinkPW'))),
      );
      console.log('ePwGinLinkPW 相关定义:', relevantLines.join('\n'));
    } else {
      console.log('⚠ 类型定义中未找到 ePwGinLinkPW');
    }
  } catch (error) {
    console.error('更新Monaco Editor类型定义失败:', error);
  }
};

// 获取TypeScript类型字符串
const getTypeScriptType = (type: string): string => {
  switch (type.toLowerCase()) {
    case 'string':
      return 'string';
    case 'number':
    case 'int':
    case 'integer':
    case 'float':
    case 'double':
      return 'number';
    case 'boolean':
    case 'bool':
      return 'boolean';
    case 'array':
      return 'any[]';
    default:
      return 'any';
  }
};

// 处理ROOT节点，展开其子节点（递归处理）
const expandRootNodes = (variables: VariableData[]): VariableData[] => {
  const result: VariableData[] = [];

  variables.forEach((variable) => {
    if (variable.key === 'ROOT' && variable.children && variable.children.length > 0) {
      // 如果是ROOT节点，递归展开其子节点
      const expandedChildren = expandRootNodes(variable.children);
      result.push(...expandedChildren);
    } else {
      // 非ROOT节点，递归处理其子节点
      const processedVariable = { ...variable };
      if (processedVariable.children && processedVariable.children.length > 0) {
        processedVariable.children = expandRootNodes(processedVariable.children);
      }
      result.push(processedVariable);
    }
  });

  return result;
};

// 生成外部字段的 .d.ts 定义文件
export const generateTypeDefinitionFromFields = (
  fields: Array<{
    name: string;
    type: string;
    description?: string;
    children?: any[];
  }>,
): string => {
  const interfaces: string[] = [];
  const declarations: string[] = [];

  // 递归生成接口定义
  const generateInterface = (fieldList: any[], interfaceName: string): string => {
    const properties = fieldList
      .map((field) => {
        const { name, type, description, children } = field;

        // 生成注释
        const comment = description ? `  /** ${description} */\n` : '';

        // 处理嵌套对象
        if (children && children.length > 0) {
          const childInterfaceName = `${interfaceName}_${name}`;
          interfaces.push(generateInterface(children, childInterfaceName));
          return `${comment}  ${name}: ${childInterfaceName};`;
        } else {
          const tsType = getTypeScriptType(type);
          return `${comment}  ${name}: ${tsType};`;
        }
      })
      .join('\n');

    return `interface ${interfaceName} {\n${properties}\n}`;
  };

  // 为每个顶级字段生成接口和声明
  fields.forEach((field) => {
    const { name, type, description, children } = field;

    if (children && children.length > 0) {
      // 生成嵌套接口
      const interfaceName = `${name.charAt(0).toUpperCase() + name.slice(1)}Type`;
      interfaces.push(generateInterface(children, interfaceName));

      // 生成变量声明
      const comment = description ? `/** ${description} */\n` : '';
      declarations.push(`${comment}declare const ${name}: ${interfaceName};`);
    } else {
      // 简单类型直接声明
      const tsType = getTypeScriptType(type);
      const comment = description ? `/** ${description} */\n` : '';
      declarations.push(`${comment}declare const ${name}: ${tsType};`);
    }
  });

  // 组合所有定义
  const allDefinitions = [...interfaces, ...declarations].join('\n\n');

  return `// 自动生成的类型定义文件\n// Generated TypeScript definitions\n\n${allDefinitions}`;
};

// 添加外部类型定义到 Monaco Editor
export const addExternalTypeDefinitions = (typeDefinitions: string, fileName: string = 'external-types.d.ts') => {
  try {
    const libUri = `ts:${fileName}`;
    monaco.languages.typescript.typescriptDefaults.addExtraLib(typeDefinitions, libUri);
    monaco.languages.typescript.javascriptDefaults.addExtraLib(typeDefinitions, libUri);

    console.log(`已添加外部类型定义: ${fileName}`);
    console.log('类型定义内容:', typeDefinitions);
  } catch (error) {
    console.error('添加外部类型定义失败:', error);
  }
};

// 更新智能提示数据的函数
export const updateIntelliSenseData = (data: {
  currentVariables?: VariableData[];
  localVariables?: VariableData[];
  globalVariables?: VariableData[];
  functions?: FunctionData[];
}) => {
  if (data.currentVariables) {
    // 处理ROOT节点展开
    currentVariables = expandRootNodes(data.currentVariables);
    console.log('更新当前变量:', currentVariables);
    // 详细打印变量结构，特别是嵌套字段
    console.log('当前变量详细结构:', JSON.stringify(currentVariables, null, 2));
  }
  if (data.localVariables) {
    // 处理ROOT节点展开
    localVariables = expandRootNodes(data.localVariables);
    console.log('更新局部变量:', localVariables);
    console.log('局部变量详细结构:', JSON.stringify(localVariables, null, 2));
  }
  if (data.globalVariables) {
    // 处理ROOT节点展开
    globalVariables = expandRootNodes(data.globalVariables);
    console.log('更新全局变量:', globalVariables);
  }
  if (data.functions) {
    functions = data.functions;
    console.log('更新函数列表:', functions);
    console.log(
      '函数详细信息:',
      functions.map((f) => ({
        value: f.value,
        label: f.label,
        parameters: f.parameters,
        returnType: f.returnType,
        examples: f.examples?.length || 0,
      })),
    );
  }

  // 更新Monaco Editor的类型定义
  updateMonacoTypeDefinitions();
};

// 扁平化变量列表，生成所有可能的路径
const flattenVariables = (
  variables: VariableData[],
  prefix: string = '',
): Array<{ path: string; variable: VariableData }> => {
  const result: Array<{ path: string; variable: VariableData }> = [];

  variables.forEach((variable) => {
    const currentPath = prefix ? `${prefix}.${variable.key}` : variable.key;
    result.push({ path: currentPath, variable });

    if (variable.children && variable.children.length > 0) {
      result.push(...flattenVariables(variable.children, currentPath));
    }
  });

  return result;
};

// 去重智能提示建议
const deduplicateSuggestions = (suggestions: any[]): any[] => {
  const seen = new Set<string>();
  return suggestions.filter((suggestion) => {
    const key = suggestion.label;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

// 获取第一级变量建议（只显示第一级，不展平）
const getFirstLevelSuggestions = (variables: VariableData[]): any[] => {
  return variables.map((variable) => ({
    label: variable.key,
    kind:
      variable.children && variable.children.length > 0
        ? monaco.languages.CompletionItemKind.Module
        : monaco.languages.CompletionItemKind.Variable,
    insertText: variable.key,
    detail: variable.type,
    documentation: variable.description || variable.pathDescription || `${variable.type} 类型变量`,
    sortText: `0_${variable.key}`,
  }));
};

// 获取函数建议
const getFunctionSuggestions = (): any[] => {
  return functions.map((func) => {
    // 生成详细的文档说明
    const documentation = generateFunctionDocumentation(func);

    // 生成函数签名作为detail
    const signature = generateFunctionSignature(func);

    return {
      label: func.value, // 使用英文函数名而不是中文label
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: func.script,
      detail: signature, // 显示函数签名
      documentation: {
        value: documentation,
        isTrusted: true,
        supportHtml: true,
      },
      sortText: `1_${func.value}`,
    };
  });
};

// 生成函数签名
const generateFunctionSignature = (func: FunctionData): string => {
  let params = '';
  if (func.parameters && func.parameters.length > 0) {
    // 直接处理所有参数，包括可变参数
    params = func.parameters
      .map((param) => {
        const optional = !param.required ? '?' : '';
        // 如果是可变参数，使用 ...paramName: any[] 语法
        if (param.isVariadic) {
          return `...${param.name}: any[]`;
        } else {
          return `${param.name}${optional}: ${param.type}`;
        }
      })
      .join(', ');
  }

  const returnType = func.returnType || 'any';
  return `${func.value}(${params}): ${returnType}`;
};

// 生成函数文档
const generateFunctionDocumentation = (func: FunctionData): string => {
  const parts = [];

  // 添加中文名称和描述
  parts.push(`**${func.label}** - ${func.remark}`);

  // 添加函数签名
  const signature = generateFunctionSignature(func);
  parts.push(`\`\`\`typescript\n${signature}\n\`\`\``);

  // 添加参数说明
  if (func.parameters && func.parameters.length > 0) {
    parts.push('**参数说明:**');
    func.parameters.forEach((param) => {
      const required = param.required ? '必需' : '可选';
      const defaultValue = param.defaultValue !== undefined ? ` (默认: ${param.defaultValue})` : '';
      parts.push(`- \`${param.name}\` (${param.type}, ${required}): ${param.description || ''}${defaultValue}`);
    });
  }

  // 添加返回值说明
  if (func.returnType) {
    parts.push(`**返回值:** ${func.outputType || func.returnType}`);
  }

  // 添加使用示例
  if (func.examples && func.examples.length > 0) {
    parts.push('**使用示例:**');
    func.examples.slice(0, 3).forEach((example, index) => {
      parts.push(`${index + 1}. **${example.title}**`);
      parts.push(`\`\`\`javascript\n${example.code}\n\`\`\``);
      if (example.result) {
        parts.push(`结果: \`${example.result}\``);
      }
      if (example.description) {
        parts.push(`说明: ${example.description}`);
      }
      parts.push('');
    });
  }

  return parts.join('\n\n');
};

// 注意：移除了不再使用的辅助函数，现在主要依赖原生 TypeScript 类型定义

// 检查是否应该提供自定义建议 - 增强逻辑，支持点号触发的嵌套属性提示
const shouldProvideCustomSuggestions = (trimmedText: string, triggerCharacter?: string): boolean => {
  // 对于点号触发，检查是否是我们定义的变量的属性访问
  if (triggerCharacter === '.') {
    // 获取点号前的变量路径
    const beforeDot = trimmedText.substring(0, trimmedText.lastIndexOf('.'));
    const variablePath = beforeDot.split(/\s+/).pop() || '';

    // 检查是否是我们定义的变量
    const isOurVariable = currentVariables.some(
      (v) =>
        v.key === variablePath ||
        variablePath.startsWith(v.key + '.') ||
        (v.path && (v.path === variablePath || variablePath.startsWith(v.path + '.'))),
    );

    console.log(`点号触发检查: ${variablePath}, 是否为我们的变量: ${isOurVariable}`);

    // 如果是我们定义的变量，让 TypeScript 类型定义处理
    // 如果不是，也让 TypeScript 处理（可能是内置对象）
    return false; // 暂时让 TypeScript 原生处理所有点号情况
  }

  // 对于非点号触发，只在输入顶级变量名时提供少量补充建议
  // 主要是为了提供更好的文档说明和示例
  const lastWord = trimmedText.split(/\s+/).pop() || '';

  // 只在输入变量名开头时提供建议，避免过度干扰
  const isTypingTopLevelVariable =
    lastWord.length > 0 &&
    lastWord.length <= 3 &&
    (currentVariables.some((v) => v.key.toLowerCase().startsWith(lastWord.toLowerCase())) ||
      lastWord.toLowerCase().includes('util') ||
      lastWord.toLowerCase().includes('data'));

  return isTypingTopLevelVariable;
};

// 注册简化的 TypeScript 智能提示提供者 - 主要依赖原生 addExtraLib
monaco.languages.registerCompletionItemProvider('typescript', {
  triggerCharacters: ['.', '_'], // 添加点号触发，确保能够提供嵌套属性的智能提示
  provideCompletionItems: async (model, position, context) => {
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    const trimmedText = textBeforePointer.trim();
    const triggerCharacter = context.triggerCharacter;

    // 只在特定情况下提供补充建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      const suggestions: any[] = [];

      // 添加顶级变量建议（主要为了提供更好的文档）
      const currentSuggestions = getFirstLevelSuggestions(currentVariables);
      suggestions.push(...currentSuggestions);

      // 添加 Utils 函数建议（主要为了提供更好的文档和示例）
      const functionSuggestions = getFunctionSuggestions();
      suggestions.push(...functionSuggestions);

      // 去重处理
      const uniqueSuggestions = deduplicateSuggestions(suggestions);

      return {
        suggestions: uniqueSuggestions,
        incomplete: true, // 始终允许默认提示继续工作
      };
    }

    // 默认情况下不提供建议，完全依赖 TypeScript 原生智能提示
    return {
      suggestions: [],
      incomplete: true,
    };
  },
});

// 同样为 JavaScript 语言注册简化的智能提示提供者
monaco.languages.registerCompletionItemProvider('javascript', {
  triggerCharacters: ['.', '_'], // 添加点号触发，确保能够提供嵌套属性的智能提示
  provideCompletionItems: async (model, position, context) => {
    const { lineNumber, column } = position;

    // 获取当前行的文本
    const textBeforePointer = model.getValueInRange({
      startLineNumber: lineNumber,
      startColumn: 0,
      endLineNumber: lineNumber,
      endColumn: column,
    });

    const trimmedText = textBeforePointer.trim();
    const triggerCharacter = context.triggerCharacter;

    // 只在特定情况下提供补充建议
    if (shouldProvideCustomSuggestions(trimmedText, triggerCharacter)) {
      const suggestions: any[] = [];

      // 添加顶级变量建议（主要为了提供更好的文档）
      const currentSuggestions = getFirstLevelSuggestions(currentVariables);
      suggestions.push(...currentSuggestions);

      // 添加 Utils 函数建议（主要为了提供更好的文档和示例）
      const functionSuggestions = getFunctionSuggestions();
      suggestions.push(...functionSuggestions);

      // 去重处理
      const uniqueSuggestions = deduplicateSuggestions(suggestions);

      return {
        suggestions: uniqueSuggestions,
        incomplete: true, // 始终允许默认提示继续工作
      };
    }

    // 默认情况下不提供建议，完全依赖 JavaScript 原生智能提示
    return {
      suggestions: [],
      incomplete: true,
    };
  },
});

// 提供Utils函数的参数提示
const provideUtilsSignatureHelp = (model: any, position: any) => {
  const { lineNumber, column } = position;

  // 获取当前行的文本
  const lineText = model.getLineContent(lineNumber);
  const textBeforePointer = lineText.substring(0, column - 1);

  // 查找Utils函数调用
  const utilsCallMatch = textBeforePointer.match(/Utils\.(\w+)\s*\(/);
  if (!utilsCallMatch) {
    return null;
  }

  const functionName = utilsCallMatch[1];
  const func = functions.find((f) => f.value === functionName);

  if (!func || !func.parameters || func.parameters.length === 0) {
    return null;
  }

  // 计算当前参数位置
  const functionCallStart = utilsCallMatch.index! + utilsCallMatch[0].length - 1; // 括号位置
  const currentParamText = textBeforePointer.substring(functionCallStart + 1);
  const commaCount = (currentParamText.match(/,/g) || []).length;

  // 检查是否有可变长度参数
  const hasVariadicParam = func.parameters.some((param) => param.isVariadic);
  const activeParameter = hasVariadicParam ? 0 : Math.min(commaCount, func.parameters.length - 1);

  // 生成参数信息
  const parameters = func.parameters.map((param) => {
    const isOptional = !param.required;
    const paramType = param.type === 'DateTime' ? 'Date' : param.type;

    let label: string;
    let documentation: string;

    if (param.isVariadic) {
      label = `...${param.name}: any[]`;
      documentation = param.description || `可变长度参数 ${param.name} - 可以传入任意数量的参数`;
    } else {
      label = `${param.name}${isOptional ? '?' : ''}: ${paramType}`;
      documentation = param.description || `参数 ${param.name}`;
    }

    return {
      label: label,
      documentation: {
        value: documentation,
        isTrusted: true,
      },
    };
  });

  // 生成函数签名
  const signature = {
    label: generateFunctionSignature(func),
    documentation: {
      value: `**${func.label}** - ${func.remark}`,
      isTrusted: true,
    },
    parameters: parameters,
    activeParameter: activeParameter,
  };

  return {
    value: {
      signatures: [signature],
      activeSignature: 0,
      activeParameter: activeParameter,
    },
    dispose: () => {},
  };
};

// 注册参数提示提供者 - TypeScript
monaco.languages.registerSignatureHelpProvider('typescript', {
  signatureHelpTriggerCharacters: ['(', ','],
  signatureHelpRetriggerCharacters: [','],
  provideSignatureHelp: (model, position) => {
    return provideUtilsSignatureHelp(model, position);
  },
});

// 注册参数提示提供者 - JavaScript
monaco.languages.registerSignatureHelpProvider('javascript', {
  signatureHelpTriggerCharacters: ['(', ','],
  signatureHelpRetriggerCharacters: [','],
  provideSignatureHelp: (model, position) => {
    return provideUtilsSignatureHelp(model, position);
  },
});
